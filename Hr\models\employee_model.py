# This file has been moved to legacy/legacy_models.py
# to avoid conflicts with the new Employee model in employee/employee_models.py

# from django.db import models
# from django.utils.translation import gettext_lazy as _

# from .base_models import Department, Job, JobInsurance, Car


# This entire file content has been moved to legacy/legacy_models.py
# All model definitions are commented out to avoid conflicts

# All legacy Employee model content has been moved to legacy/legacy_models.py
# This file is now empty to avoid conflicts with the new Employee model structure
