# Generated by Django 5.0.14 on 2025-07-08 17:21

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Hr', '0005_alter_employeenote_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='اسم الفرع أو الموقع', max_length=200, verbose_name='اسم الفرع')),
                ('code', models.CharField(help_text='كود فريد للفرع', max_length=20, unique=True, validators=[django.core.validators.RegexValidator(message='كود الفرع يجب أن يحتوي على أحرف كبيرة وأرقام فقط', regex='^[A-Z0-9\\-]+$')], verbose_name='كود الفرع')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الفرع')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('fax', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الفاكس')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='المدينة')),
                ('state', models.CharField(blank=True, max_length=100, null=True, verbose_name='المحافظة/الولاية')),
                ('country', models.CharField(default='مصر', max_length=100, verbose_name='الدولة')),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='الرمز البريدي')),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, help_text='خط العرض للموقع الجغرافي', max_digits=10, null=True, verbose_name='خط العرض')),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, help_text='خط الطول للموقع الجغرافي', max_digits=11, null=True, verbose_name='خط الطول')),
                ('opening_time', models.TimeField(blank=True, null=True, verbose_name='وقت بداية العمل')),
                ('closing_time', models.TimeField(blank=True, null=True, verbose_name='وقت نهاية العمل')),
                ('timezone', models.CharField(blank=True, help_text='المنطقة الزمنية للفرع إذا كانت مختلفة عن الشركة', max_length=50, null=True, verbose_name='المنطقة الزمنية')),
                ('employee_capacity', models.PositiveIntegerField(blank=True, null=True, verbose_name='السعة القصوى للموظفين')),
                ('floor_area', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='المساحة (متر مربع)')),
                ('parking_spaces', models.PositiveIntegerField(blank=True, null=True, verbose_name='عدد أماكن الانتظار')),
                ('monthly_rent', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='الإيجار الشهري')),
                ('monthly_utilities', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='المرافق الشهرية')),
                ('branch_settings', models.JSONField(default=dict, help_text='إعدادات خاصة بالفرع', verbose_name='إعدادات الفرع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_headquarters', models.BooleanField(default=False, help_text='هل هذا الفرع هو المقر الرئيسي للشركة؟', verbose_name='المقر الرئيسي')),
                ('established_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التأسيس')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فرع',
                'verbose_name_plural': 'الفروع',
                'db_table': 'hrms_branch',
                'ordering': ['company', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='الاسم التجاري للشركة', max_length=200, verbose_name='اسم الشركة')),
                ('legal_name', models.CharField(blank=True, help_text='الاسم القانوني المسجل للشركة', max_length=200, null=True, verbose_name='الاسم القانوني')),
                ('tax_id', models.CharField(blank=True, max_length=50, null=True, unique=True, validators=[django.core.validators.RegexValidator(message='الرقم الضريبي يجب أن يحتوي على أرقام وحروف فقط', regex='^[0-9A-Za-z\\-]+$')], verbose_name='الرقم الضريبي')),
                ('registration_number', models.CharField(blank=True, help_text='رقم السجل التجاري للشركة', max_length=50, null=True, verbose_name='رقم السجل التجاري')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('website', models.URLField(blank=True, null=True, verbose_name='الموقع الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='المدينة')),
                ('country', models.CharField(default='مصر', max_length=100, verbose_name='الدولة')),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='الرمز البريدي')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos/', verbose_name='شعار الشركة')),
                ('primary_color', models.CharField(default='#007bff', help_text='اللون الأساسي لواجهة النظام (Hex Color)', max_length=7, verbose_name='اللون الأساسي')),
                ('secondary_color', models.CharField(default='#6c757d', help_text='اللون الثانوي لواجهة النظام (Hex Color)', max_length=7, verbose_name='اللون الثانوي')),
                ('industry', models.CharField(blank=True, max_length=100, null=True, verbose_name='نوع النشاط')),
                ('established_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التأسيس')),
                ('employee_count', models.PositiveIntegerField(default=0, help_text='العدد التقريبي للموظفين', verbose_name='عدد الموظفين')),
                ('timezone', models.CharField(default='Africa/Cairo', max_length=50, verbose_name='المنطقة الزمنية')),
                ('currency', models.CharField(default='EGP', help_text='رمز العملة (ISO 4217)', max_length=3, verbose_name='العملة')),
                ('fiscal_year_start', models.DateField(blank=True, null=True, verbose_name='بداية السنة المالية')),
                ('hr_settings', models.JSONField(default=dict, help_text='إعدادات خاصة بنظام الموارد البشرية', verbose_name='إعدادات الموارد البشرية')),
                ('payroll_settings', models.JSONField(default=dict, help_text='إعدادات خاصة بنظام الرواتب', verbose_name='إعدادات الرواتب')),
                ('leave_settings', models.JSONField(default=dict, help_text='إعدادات خاصة بنظام الإجازات', verbose_name='إعدادات الإجازات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'شركة',
                'verbose_name_plural': 'الشركات',
                'db_table': 'hrms_company',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('personal', 'وثائق شخصية'), ('identification', 'وثائق هوية'), ('education', 'شهادات تعليمية'), ('professional', 'شهادات مهنية'), ('medical', 'وثائق طبية'), ('contract', 'عقود ووثائق عمل'), ('insurance', 'وثائق تأمين'), ('financial', 'وثائق مالية'), ('legal', 'وثائق قانونية'), ('training', 'شهادات تدريب'), ('performance', 'تقييمات أداء'), ('disciplinary', 'إجراءات تأديبية'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الوثيقة')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الوثيقة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الوثيقة')),
                ('document_number', models.CharField(blank=True, help_text='رقم الوثيقة الرسمي إن وجد', max_length=100, null=True, verbose_name='رقم الوثيقة')),
                ('file', models.FileField(upload_to='employee_documents/', verbose_name='الملف')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='حجم الملف (بايت)')),
                ('mime_type', models.CharField(blank=True, max_length=100, null=True, verbose_name='نوع الملف')),
                ('issue_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإصدار')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('issuing_authority', models.CharField(blank=True, max_length=200, null=True, verbose_name='جهة الإصدار')),
                ('issuing_country', models.CharField(blank=True, max_length=100, null=True, verbose_name='دولة الإصدار')),
                ('is_verified', models.BooleanField(default=False, verbose_name='تم التحقق')),
                ('verified_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التحقق')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('expired', 'منتهي الصلاحية'), ('replaced', 'تم استبداله'), ('archived', 'مؤرشف'), ('pending_verification', 'في انتظار التحقق'), ('rejected', 'مرفوض')], default='pending_verification', max_length=20, verbose_name='حالة الوثيقة')),
                ('version', models.PositiveIntegerField(default=1, verbose_name='الإصدار')),
                ('is_confidential', models.BooleanField(default=False, help_text='هل هذه الوثيقة سرية؟', verbose_name='سري')),
                ('access_level', models.CharField(choices=[('public', 'عام'), ('internal', 'داخلي'), ('confidential', 'سري'), ('restricted', 'مقيد')], default='internal', max_length=20, verbose_name='مستوى الوصول')),
                ('reminder_days_before_expiry', models.PositiveIntegerField(default=30, verbose_name='تذكير قبل انتهاء الصلاحية (أيام)')),
                ('tags', models.JSONField(default=list, help_text='علامات للبحث والتصنيف', verbose_name='العلامات')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'وثيقة موظف',
                'verbose_name_plural': 'وثائق الموظفين',
                'db_table': 'hrms_employee_document',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeEmergencyContact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=200, verbose_name='الاسم الكامل')),
                ('relationship', models.CharField(choices=[('spouse', 'زوج/زوجة'), ('father', 'والد'), ('mother', 'والدة'), ('son', 'ابن'), ('daughter', 'ابنة'), ('brother', 'أخ'), ('sister', 'أخت'), ('grandfather', 'جد'), ('grandmother', 'جدة'), ('uncle', 'عم/خال'), ('aunt', 'عمة/خالة'), ('cousin', 'ابن عم/خال'), ('friend', 'صديق'), ('colleague', 'زميل عمل'), ('neighbor', 'جار'), ('guardian', 'ولي أمر'), ('other', 'أخرى')], max_length=20, verbose_name='صلة القرابة')),
                ('relationship_other', models.CharField(blank=True, help_text="حدد صلة القرابة إذا اخترت 'أخرى'", max_length=100, null=True, verbose_name='صلة القرابة (أخرى)')),
                ('primary_phone', models.CharField(max_length=20, validators=[django.core.validators.RegexValidator(message='رقم الهاتف غير صحيح', regex='^\\+?[\\d\\s\\-\\(\\)]+$')], verbose_name='رقم الهاتف الأساسي')),
                ('secondary_phone', models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator(message='رقم الهاتف غير صحيح', regex='^\\+?[\\d\\s\\-\\(\\)]+$')], verbose_name='رقم الهاتف الثانوي')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='المدينة')),
                ('country', models.CharField(default='مصر', max_length=100, verbose_name='الدولة')),
                ('occupation', models.CharField(blank=True, max_length=100, null=True, verbose_name='المهنة')),
                ('workplace', models.CharField(blank=True, max_length=200, null=True, verbose_name='مكان العمل')),
                ('priority', models.PositiveIntegerField(choices=[(1, 'الأولوية الأولى'), (2, 'الأولوية الثانية'), (3, 'الأولوية الثالثة'), (4, 'الأولوية الرابعة'), (5, 'الأولوية الخامسة')], default=1, help_text='أولوية الاتصال في حالات الطوارئ', verbose_name='الأولوية')),
                ('is_primary', models.BooleanField(default=False, help_text='هل هذه جهة الاتصال الأساسية؟', verbose_name='جهة الاتصال الأساسية')),
                ('best_time_to_call', models.CharField(blank=True, max_length=100, null=True, verbose_name='أفضل وقت للاتصال')),
                ('availability_notes', models.TextField(blank=True, help_text='معلومات إضافية حول أوقات التوفر', null=True, verbose_name='ملاحظات التوفر')),
                ('can_make_medical_decisions', models.BooleanField(default=False, help_text='هل يمكن لهذا الشخص اتخاذ قرارات طبية نيابة عن الموظف؟', verbose_name='يمكنه اتخاذ قرارات طبية')),
                ('can_receive_salary', models.BooleanField(default=False, help_text='هل يمكن لهذا الشخص استلام راتب الموظف في حالات الطوارئ؟', verbose_name='يمكنه استلام الراتب')),
                ('has_power_of_attorney', models.BooleanField(default=False, help_text='هل لدى هذا الشخص توكيل قانوني من الموظف؟', verbose_name='لديه توكيل قانوني')),
                ('preferred_language', models.CharField(choices=[('ar', 'العربية'), ('en', 'الإنجليزية'), ('fr', 'الفرنسية'), ('other', 'أخرى')], default='ar', max_length=50, verbose_name='اللغة المفضلة')),
                ('is_verified', models.BooleanField(default=False, help_text='هل تم التحقق من صحة معلومات الاتصال؟', verbose_name='تم التحقق')),
                ('verified_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التحقق')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات إضافية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'جهة اتصال طوارئ',
                'verbose_name_plural': 'جهات اتصال الطوارئ',
                'db_table': 'hrms_employee_emergency_contact',
                'ordering': ['employee', 'priority'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeShiftAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assignment_type', models.CharField(choices=[('permanent', 'دائم'), ('temporary', 'مؤقت'), ('rotating', 'متناوب'), ('flexible', 'مرن'), ('on_call', 'عند الطلب')], default='permanent', max_length=15, verbose_name='نوع التعيين')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, help_text='اتركه فارغاً للتعيين الدائم', null=True, verbose_name='تاريخ النهاية')),
                ('specific_dates', models.JSONField(default=list, help_text='قائمة بالتواريخ المحددة للتعيين المرن', verbose_name='تواريخ محددة')),
                ('weekly_pattern', models.JSONField(default=dict, help_text='نمط الوردية الأسبوعية للتناوب', verbose_name='النمط الأسبوعي')),
                ('override_shift_settings', models.BooleanField(default=False, help_text='هل تريد تجاوز إعدادات الوردية الافتراضية؟', verbose_name='تجاوز إعدادات الوردية')),
                ('custom_start_time', models.TimeField(blank=True, null=True, verbose_name='وقت البداية المخصص')),
                ('custom_end_time', models.TimeField(blank=True, null=True, verbose_name='وقت النهاية المخصص')),
                ('custom_break_duration', models.PositiveIntegerField(blank=True, null=True, verbose_name='مدة الراحة المخصصة (دقائق)')),
                ('requires_approval', models.BooleanField(default=False, verbose_name='يتطلب موافقة')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('expired', 'منتهي'), ('cancelled', 'ملغي')], default='active', max_length=15, verbose_name='الحالة')),
                ('priority', models.PositiveIntegerField(default=1, help_text='الأولوية في حالة تداخل التعيينات (الأقل رقماً له الأولوية)', verbose_name='الأولوية')),
                ('reason', models.CharField(blank=True, max_length=200, null=True, verbose_name='سبب التعيين')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('auto_expire', models.BooleanField(default=True, help_text='هل ينتهي التعيين تلقائياً في تاريخ النهاية؟', verbose_name='انتهاء تلقائي')),
                ('notify_employee', models.BooleanField(default=True, verbose_name='إشعار الموظف')),
                ('notify_manager', models.BooleanField(default=True, verbose_name='إشعار المدير')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'تعيين وردية موظف',
                'verbose_name_plural': 'تعيينات ورديات الموظفين',
                'db_table': 'hrms_employee_shift_assignment',
                'ordering': ['-start_date', 'priority'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeTraining',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التدريب')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف التدريب')),
                ('provider', models.CharField(max_length=200, verbose_name='مقدم التدريب')),
                ('instructor', models.CharField(blank=True, max_length=200, null=True, verbose_name='المدرب')),
                ('training_type', models.CharField(choices=[('internal', 'تدريب داخلي'), ('external', 'تدريب خارجي'), ('online', 'تدريب إلكتروني'), ('workshop', 'ورشة عمل'), ('seminar', 'ندوة'), ('conference', 'مؤتمر'), ('certification', 'شهادة مهنية'), ('orientation', 'تدريب تعريفي'), ('safety', 'تدريب سلامة'), ('technical', 'تدريب تقني'), ('soft_skills', 'مهارات شخصية'), ('leadership', 'تدريب قيادي')], max_length=20, verbose_name='نوع التدريب')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('duration_hours', models.PositiveIntegerField(blank=True, null=True, verbose_name='مدة التدريب (ساعات)')),
                ('status', models.CharField(choices=[('planned', 'مخطط'), ('in_progress', 'جاري'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('postponed', 'مؤجل')], default='planned', max_length=20, verbose_name='الحالة')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='النتيجة')),
                ('max_score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='النتيجة العظمى')),
                ('grade', models.CharField(blank=True, max_length=10, null=True, verbose_name='التقدير')),
                ('passed', models.BooleanField(default=False, verbose_name='نجح')),
                ('certificate_issued', models.BooleanField(default=False, verbose_name='تم إصدار شهادة')),
                ('certificate_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم الشهادة')),
                ('certificate_expiry', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الشهادة')),
                ('certificate_file', models.FileField(blank=True, null=True, upload_to='training_certificates/', verbose_name='ملف الشهادة')),
                ('cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='التكلفة')),
                ('currency', models.CharField(default='EGP', max_length=3, verbose_name='العملة')),
                ('approval_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('skills_gained', models.JSONField(default=list, help_text='قائمة بالمهارات التي تم اكتسابها', verbose_name='المهارات المكتسبة')),
                ('feedback', models.TextField(blank=True, null=True, verbose_name='التقييم والملاحظات')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'تدريب موظف',
                'verbose_name_plural': 'تدريبات الموظفين',
                'db_table': 'hrms_employee_training',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='JobPosition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='المسمى الوظيفي')),
                ('code', models.CharField(help_text='كود فريد للوظيفة', max_length=20, unique=True, validators=[django.core.validators.RegexValidator(message='كود الوظيفة يجب أن يحتوي على أحرف كبيرة وأرقام فقط', regex='^[A-Z0-9\\-]+$')], verbose_name='كود الوظيفة')),
                ('title_english', models.CharField(blank=True, max_length=200, null=True, verbose_name='المسمى الوظيفي بالإنجليزية')),
                ('description', models.TextField(help_text='وصف مفصل للوظيفة ومسؤولياتها', verbose_name='وصف الوظيفة')),
                ('requirements', models.TextField(help_text='المؤهلات والخبرات المطلوبة', verbose_name='متطلبات الوظيفة')),
                ('responsibilities', models.TextField(help_text='المسؤوليات الأساسية للوظيفة', verbose_name='المسؤوليات')),
                ('skills_required', models.JSONField(default=list, help_text='قائمة بالمهارات المطلوبة للوظيفة', verbose_name='المهارات المطلوبة')),
                ('minimum_education', models.CharField(choices=[('high_school', 'ثانوية عامة'), ('diploma', 'دبلوم'), ('bachelor', 'بكالوريوس'), ('master', 'ماجستير'), ('phd', 'دكتوراه'), ('professional', 'شهادة مهنية')], default='high_school', max_length=20, verbose_name='الحد الأدنى للتعليم')),
                ('preferred_education', models.CharField(blank=True, choices=[('high_school', 'ثانوية عامة'), ('diploma', 'دبلوم'), ('bachelor', 'بكالوريوس'), ('master', 'ماجستير'), ('phd', 'دكتوراه'), ('professional', 'شهادة مهنية')], max_length=20, null=True, verbose_name='التعليم المفضل')),
                ('minimum_experience_years', models.PositiveIntegerField(default=0, verbose_name='سنوات الخبرة المطلوبة (الحد الأدنى)')),
                ('preferred_experience_years', models.PositiveIntegerField(blank=True, null=True, verbose_name='سنوات الخبرة المفضلة')),
                ('level', models.PositiveIntegerField(choices=[(1, 'مبتدئ'), (2, 'متوسط'), (3, 'أول'), (4, 'رئيس'), (5, 'مدير'), (6, 'مدير أول'), (7, 'مدير عام'), (8, 'نائب رئيس'), (9, 'رئيس'), (10, 'رئيس تنفيذي')], default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)], verbose_name='مستوى الوظيفة')),
                ('employment_type', models.CharField(choices=[('full_time', 'دوام كامل'), ('part_time', 'دوام جزئي'), ('contract', 'تعاقد'), ('temporary', 'مؤقت'), ('intern', 'متدرب'), ('consultant', 'استشاري')], default='full_time', max_length=20, verbose_name='نوع التوظيف')),
                ('min_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='الحد الأدنى للراتب')),
                ('max_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='الحد الأقصى للراتب')),
                ('currency', models.CharField(default='EGP', max_length=3, verbose_name='العملة')),
                ('benefits', models.JSONField(default=list, help_text='قائمة بالمزايا والبدلات المرتبطة بالوظيفة', verbose_name='المزايا والبدلات')),
                ('working_hours_per_week', models.PositiveIntegerField(default=40, verbose_name='ساعات العمل الأسبوعية')),
                ('travel_required', models.BooleanField(default=False, verbose_name='يتطلب سفر')),
                ('remote_work_allowed', models.BooleanField(default=False, verbose_name='يسمح بالعمل عن بُعد')),
                ('overtime_eligible', models.BooleanField(default=True, verbose_name='مؤهل للعمل الإضافي')),
                ('kpis', models.JSONField(default=list, help_text='مؤشرات الأداء الرئيسية للوظيفة', verbose_name='مؤشرات الأداء الرئيسية')),
                ('performance_goals', models.JSONField(default=list, help_text='الأهداف المتوقعة من شاغل الوظيفة', verbose_name='أهداف الأداء')),
                ('job_settings', models.JSONField(default=dict, help_text='إعدادات خاصة بالوظيفة', verbose_name='إعدادات الوظيفة')),
                ('max_headcount', models.PositiveIntegerField(default=1, help_text='العدد الأقصى للموظفين في هذه الوظيفة', verbose_name='العدد الأقصى للموظفين')),
                ('current_headcount', models.PositiveIntegerField(default=0, verbose_name='العدد الحالي للموظفين')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_critical', models.BooleanField(default=False, help_text='هل هذه الوظيفة حرجة لعمل المؤسسة؟', verbose_name='وظيفة حرجة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'وظيفة',
                'verbose_name_plural': 'الوظائف',
                'db_table': 'hrms_job_position',
                'ordering': ['department', 'level', 'title'],
            },
        ),
        migrations.CreateModel(
            name='LeaveApproval',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('approval_level', models.PositiveIntegerField(verbose_name='مستوى الموافقة')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('approved', 'موافق'), ('rejected', 'مرفوض')], max_length=10, verbose_name='حالة الموافقة')),
                ('comments', models.TextField(blank=True, null=True, verbose_name='تعليقات')),
                ('approved_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الموافقة')),
            ],
            options={
                'verbose_name': 'موافقة إجازة',
                'verbose_name_plural': 'موافقات الإجازات',
                'db_table': 'hrms_leave_approval',
                'ordering': ['approval_level'],
            },
        ),
        migrations.CreateModel(
            name='LeaveBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('balance_year', models.PositiveIntegerField(verbose_name='سنة الرصيد')),
                ('period_start_date', models.DateField(verbose_name='تاريخ بداية الفترة')),
                ('period_end_date', models.DateField(verbose_name='تاريخ نهاية الفترة')),
                ('opening_balance', models.DecimalField(decimal_places=1, default=0, max_digits=6, verbose_name='الرصيد الافتتاحي')),
                ('accrued_balance', models.DecimalField(decimal_places=1, default=0, max_digits=6, verbose_name='الرصيد المستحق')),
                ('used_balance', models.DecimalField(decimal_places=1, default=0, max_digits=6, verbose_name='الرصيد المستخدم')),
                ('pending_balance', models.DecimalField(decimal_places=1, default=0, help_text='الرصيد المحجوز للطلبات المعلقة', max_digits=6, verbose_name='الرصيد المعلق')),
                ('carried_forward_balance', models.DecimalField(decimal_places=1, default=0, max_digits=6, verbose_name='الرصيد المرحل')),
                ('adjustment_balance', models.DecimalField(decimal_places=1, default=0, max_digits=6, verbose_name='تعديل الرصيد')),
                ('encashed_balance', models.DecimalField(decimal_places=1, default=0, max_digits=6, verbose_name='الرصيد المصروف')),
                ('current_balance', models.DecimalField(decimal_places=1, default=0, max_digits=6, verbose_name='الرصيد الحالي')),
                ('available_balance', models.DecimalField(decimal_places=1, default=0, max_digits=6, verbose_name='الرصيد المتاح')),
                ('carry_forward_from_previous', models.DecimalField(decimal_places=1, default=0, max_digits=6, verbose_name='مرحل من السنة السابقة')),
                ('carry_forward_expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الرصيد المرحل')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_frozen', models.BooleanField(default=False, help_text='هل الرصيد مجمد ولا يمكن استخدامه؟', verbose_name='مجمد')),
                ('freeze_reason', models.CharField(blank=True, max_length=200, null=True, verbose_name='سبب التجميد')),
                ('last_accrual_date', models.DateField(blank=True, null=True, verbose_name='تاريخ آخر استحقاق')),
                ('last_calculation_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر حساب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'رصيد إجازة',
                'verbose_name_plural': 'أرصدة الإجازات',
                'db_table': 'hrms_leave_balance',
                'ordering': ['employee', 'leave_type', '-balance_year'],
            },
        ),
        migrations.CreateModel(
            name='LeavePolicy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم سياسة الإجازات')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود السياسة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف السياسة')),
                ('employment_types', models.CharField(choices=[('all', 'جميع أنواع التوظيف'), ('full_time', 'دوام كامل'), ('part_time', 'دوام جزئي'), ('contract', 'تعاقد'), ('temporary', 'مؤقت'), ('intern', 'متدرب')], default='all', max_length=20, verbose_name='أنواع التوظيف')),
                ('gender_criteria', models.CharField(choices=[('all', 'الجميع'), ('male', 'ذكور فقط'), ('female', 'إناث فقط')], default='all', max_length=10, verbose_name='معايير الجنس')),
                ('minimum_service_months', models.PositiveIntegerField(default=0, verbose_name='الحد الأدنى لشهور الخدمة')),
                ('maximum_service_months', models.PositiveIntegerField(blank=True, null=True, verbose_name='الحد الأقصى لشهور الخدمة')),
                ('policy_rules', models.JSONField(default=dict, help_text='قواعد مخصصة للسياسة', verbose_name='قواعد السياسة')),
                ('leave_entitlements', models.JSONField(default=dict, help_text='استحقاقات مخصصة لكل نوع إجازة', verbose_name='استحقاقات الإجازات')),
                ('approval_workflow', models.JSONField(default=list, help_text='تسلسل الموافقات المطلوبة', verbose_name='سير عمل الموافقة')),
                ('calendar_year_type', models.CharField(choices=[('calendar', 'سنة ميلادية'), ('fiscal', 'سنة مالية'), ('hire_date', 'من تاريخ التوظيف'), ('custom', 'مخصص')], default='calendar', max_length=20, verbose_name='نوع السنة')),
                ('fiscal_year_start_month', models.PositiveIntegerField(blank=True, help_text='رقم الشهر (1-12)', null=True, verbose_name='شهر بداية السنة المالية')),
                ('global_carry_forward_allowed', models.BooleanField(default=False, verbose_name='السماح بالترحيل العام')),
                ('global_max_carry_forward_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='النسبة القصوى للترحيل العام (%)')),
                ('allow_leave_encashment', models.BooleanField(default=False, verbose_name='السماح بصرف الإجازات')),
                ('max_encashment_days_per_year', models.PositiveIntegerField(blank=True, null=True, verbose_name='الحد الأقصى لأيام الصرف سنوياً')),
                ('encashment_rate_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='نسبة صرف الإجازات (%)')),
                ('notification_settings', models.JSONField(default=dict, help_text='إعدادات التنبيهات والإشعارات', verbose_name='إعدادات التنبيهات')),
                ('priority', models.PositiveIntegerField(default=1, help_text='أولوية تطبيق السياسة (الأقل رقماً له الأولوية)', verbose_name='الأولوية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_default', models.BooleanField(default=False, help_text='هل هذه السياسة الافتراضية؟', verbose_name='افتراضي')),
                ('effective_from', models.DateField(verbose_name='ساري من تاريخ')),
                ('effective_to', models.DateField(blank=True, null=True, verbose_name='ساري حتى تاريخ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'سياسة إجازات',
                'verbose_name_plural': 'سياسات الإجازات',
                'db_table': 'hrms_leave_policy',
                'ordering': ['priority', 'name'],
            },
        ),
        migrations.CreateModel(
            name='LeaveRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('days_requested', models.DecimalField(decimal_places=1, max_digits=5, verbose_name='عدد الأيام المطلوبة')),
                ('is_half_day', models.BooleanField(default=False, verbose_name='نصف يوم')),
                ('half_day_period', models.CharField(blank=True, choices=[('morning', 'الفترة الصباحية'), ('afternoon', 'الفترة المسائية')], max_length=10, null=True, verbose_name='فترة نصف اليوم')),
                ('reason', models.TextField(verbose_name='سبب الإجازة')),
                ('emergency_contact_during_leave', models.CharField(blank=True, max_length=200, null=True, verbose_name='جهة الاتصال أثناء الإجازة')),
                ('emergency_phone_during_leave', models.CharField(blank=True, max_length=20, null=True, verbose_name='هاتف الطوارئ أثناء الإجازة')),
                ('medical_certificate', models.FileField(blank=True, null=True, upload_to='leave_documents/medical/', verbose_name='الشهادة الطبية')),
                ('supporting_documents', models.FileField(blank=True, null=True, upload_to='leave_documents/supporting/', verbose_name='المستندات الداعمة')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('submitted', 'مقدم'), ('pending_approval', 'في انتظار الموافقة'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض'), ('cancelled', 'ملغي'), ('in_progress', 'جاري'), ('completed', 'مكتمل'), ('partially_taken', 'مأخوذ جزئياً')], default='draft', max_length=20, verbose_name='الحالة')),
                ('current_approval_level', models.PositiveIntegerField(default=0, verbose_name='مستوى الموافقة الحالي')),
                ('balance_before_request', models.DecimalField(blank=True, decimal_places=1, max_digits=6, null=True, verbose_name='الرصيد قبل الطلب')),
                ('balance_after_request', models.DecimalField(blank=True, decimal_places=1, max_digits=6, null=True, verbose_name='الرصيد بعد الطلب')),
                ('actual_start_date', models.DateField(blank=True, null=True, verbose_name='تاريخ البداية الفعلي')),
                ('actual_end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ النهاية الفعلي')),
                ('actual_days_taken', models.DecimalField(blank=True, decimal_places=1, max_digits=5, null=True, verbose_name='الأيام المأخوذة فعلياً')),
                ('expected_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ العودة المتوقع')),
                ('actual_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ العودة الفعلي')),
                ('return_confirmed', models.BooleanField(default=False, verbose_name='تم تأكيد العودة')),
                ('return_confirmed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ تأكيد العودة')),
                ('handover_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات التسليم')),
                ('handover_completed', models.BooleanField(default=False, verbose_name='تم التسليم')),
                ('employee_comments', models.TextField(blank=True, null=True, verbose_name='تعليقات الموظف')),
                ('manager_comments', models.TextField(blank=True, null=True, verbose_name='تعليقات المدير')),
                ('hr_comments', models.TextField(blank=True, null=True, verbose_name='تعليقات الموارد البشرية')),
                ('is_emergency_leave', models.BooleanField(default=False, verbose_name='إجازة طارئة')),
                ('is_backdated', models.BooleanField(default=False, verbose_name='طلب بأثر رجعي')),
                ('auto_approved', models.BooleanField(default=False, verbose_name='موافقة تلقائية')),
                ('submitted_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التقديم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'طلب إجازة',
                'verbose_name_plural': 'طلبات الإجازات',
                'db_table': 'hrms_leave_request',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LeaveTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('accrual', 'استحقاق'), ('deduct', 'خصم'), ('adjustment', 'تعديل'), ('carry_forward', 'ترحيل'), ('encashment', 'صرف'), ('reserve', 'حجز'), ('release', 'إلغاء حجز'), ('opening', 'رصيد افتتاحي'), ('expiry', 'انتهاء صلاحية')], max_length=20, verbose_name='نوع المعاملة')),
                ('amount', models.DecimalField(decimal_places=1, max_digits=6, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=500, verbose_name='الوصف')),
                ('reference_id', models.CharField(blank=True, help_text='رقم مرجعي للمعاملة (مثل رقم طلب الإجازة)', max_length=100, null=True, verbose_name='رقم المرجع')),
                ('balance_before_transaction', models.DecimalField(blank=True, decimal_places=1, max_digits=6, null=True, verbose_name='الرصيد قبل المعاملة')),
                ('balance_after_transaction', models.DecimalField(decimal_places=1, max_digits=6, verbose_name='الرصيد بعد المعاملة')),
                ('processed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ المعاملة')),
            ],
            options={
                'verbose_name': 'معاملة رصيد إجازة',
                'verbose_name_plural': 'معاملات أرصدة الإجازات',
                'db_table': 'hrms_leave_transaction',
                'ordering': ['-processed_at'],
            },
        ),
        migrations.CreateModel(
            name='SalaryComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المكون')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المكون')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف المكون')),
                ('component_type', models.CharField(choices=[('basic_salary', 'الراتب الأساسي'), ('allowance', 'بدل'), ('bonus', 'مكافأة'), ('commission', 'عمولة'), ('overtime', 'عمل إضافي'), ('deduction', 'خصم'), ('tax', 'ضريبة'), ('insurance', 'تأمين'), ('loan', 'قرض'), ('advance', 'سلفة'), ('penalty', 'غرامة'), ('other', 'أخرى')], max_length=20, verbose_name='نوع المكون')),
                ('category', models.CharField(choices=[('earning', 'استحقاق'), ('deduction', 'خصم'), ('employer_contribution', 'مساهمة صاحب العمل')], max_length=25, verbose_name='فئة المكون')),
                ('calculation_method', models.CharField(choices=[('fixed', 'مبلغ ثابت'), ('percentage', 'نسبة مئوية'), ('formula', 'معادلة'), ('attendance_based', 'على أساس الحضور'), ('performance_based', 'على أساس الأداء'), ('slab', 'شرائح')], max_length=20, verbose_name='طريقة الحساب')),
                ('fixed_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='المبلغ الثابت')),
                ('percentage_value', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='النسبة المئوية')),
                ('calculation_formula', models.TextField(blank=True, help_text='معادلة لحساب المكون (مثل: basic_salary * 0.1)', null=True, verbose_name='معادلة الحساب')),
                ('percentage_basis', models.CharField(blank=True, choices=[('basic_salary', 'الراتب الأساسي'), ('gross_salary', 'إجمالي الراتب'), ('total_earnings', 'إجمالي الاستحقاقات'), ('specific_component', 'مكون محدد'), ('attendance_days', 'أيام الحضور'), ('working_hours', 'ساعات العمل')], max_length=20, null=True, verbose_name='أساس النسبة المئوية')),
                ('minimum_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='الحد الأدنى')),
                ('maximum_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='الحد الأقصى')),
                ('is_taxable', models.BooleanField(default=True, verbose_name='خاضع للضريبة')),
                ('is_insurance_applicable', models.BooleanField(default=True, verbose_name='خاضع للتأمين')),
                ('affects_overtime', models.BooleanField(default=False, verbose_name='يؤثر على العمل الإضافي')),
                ('frequency', models.CharField(choices=[('monthly', 'شهري'), ('quarterly', 'ربع سنوي'), ('semi_annual', 'نصف سنوي'), ('annual', 'سنوي'), ('one_time', 'مرة واحدة'), ('variable', 'متغير')], default='monthly', max_length=15, verbose_name='التكرار')),
                ('effective_from', models.DateField(blank=True, null=True, verbose_name='ساري من')),
                ('effective_to', models.DateField(blank=True, null=True, verbose_name='ساري حتى')),
                ('conditions', models.JSONField(default=dict, help_text='شروط تطبيق المكون', verbose_name='الشروط')),
                ('calculation_rules', models.JSONField(default=dict, help_text='قواعد إضافية لحساب المكون', verbose_name='قواعد الحساب')),
                ('slabs', models.JSONField(default=list, help_text='تكوين الشرائح للحساب المتدرج', verbose_name='الشرائح')),
                ('display_order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
                ('show_in_payslip', models.BooleanField(default=True, verbose_name='إظهار في قسيمة الراتب')),
                ('show_in_summary', models.BooleanField(default=True, verbose_name='إظهار في الملخص')),
                ('account_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='كود الحساب')),
                ('cost_center', models.CharField(blank=True, max_length=20, null=True, verbose_name='مركز التكلفة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_system_component', models.BooleanField(default=False, help_text='هل هذا مكون أساسي في النظام؟', verbose_name='مكون نظام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مكون راتب',
                'verbose_name_plural': 'مكونات الرواتب',
                'db_table': 'hrms_salary_component',
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='WorkShift',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الوردية')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الوردية')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الوردية')),
                ('shift_type', models.CharField(choices=[('morning', 'صباحي'), ('afternoon', 'مسائي'), ('evening', 'ليلي'), ('night', 'ليلي متأخر'), ('rotating', 'متناوب'), ('flexible', 'مرن'), ('split', 'منقسم')], max_length=20, verbose_name='نوع الوردية')),
                ('start_time', models.TimeField(verbose_name='وقت البداية')),
                ('end_time', models.TimeField(verbose_name='وقت النهاية')),
                ('break_start_time', models.TimeField(blank=True, null=True, verbose_name='بداية فترة الراحة')),
                ('break_end_time', models.TimeField(blank=True, null=True, verbose_name='نهاية فترة الراحة')),
                ('break_duration_minutes', models.PositiveIntegerField(default=60, verbose_name='مدة الراحة (دقائق)')),
                ('total_hours', models.DecimalField(decimal_places=2, max_digits=4, verbose_name='إجمالي ساعات العمل')),
                ('working_hours', models.DecimalField(decimal_places=2, help_text='ساعات العمل بعد خصم فترات الراحة', max_digits=4, verbose_name='ساعات العمل الفعلية')),
                ('late_grace_minutes', models.PositiveIntegerField(default=15, verbose_name='فترة سماح التأخير (دقائق)')),
                ('early_leave_grace_minutes', models.PositiveIntegerField(default=15, verbose_name='فترة سماح الانصراف المبكر (دقائق)')),
                ('overtime_threshold_minutes', models.PositiveIntegerField(default=30, help_text='الحد الأدنى للدقائق الإضافية لاحتسابها كعمل إضافي', verbose_name='حد العمل الإضافي (دقائق)')),
                ('max_overtime_hours', models.DecimalField(decimal_places=2, default=4.0, max_digits=4, verbose_name='الحد الأقصى للعمل الإضافي (ساعات)')),
                ('monday', models.BooleanField(default=True, verbose_name='الاثنين')),
                ('tuesday', models.BooleanField(default=True, verbose_name='الثلاثاء')),
                ('wednesday', models.BooleanField(default=True, verbose_name='الأربعاء')),
                ('thursday', models.BooleanField(default=True, verbose_name='الخميس')),
                ('friday', models.BooleanField(default=True, verbose_name='الجمعة')),
                ('saturday', models.BooleanField(default=False, verbose_name='السبت')),
                ('sunday', models.BooleanField(default=False, verbose_name='الأحد')),
                ('is_night_shift', models.BooleanField(default=False, help_text='هل هذه وردية ليلية تمتد لليوم التالي؟', verbose_name='وردية ليلية')),
                ('requires_check_in', models.BooleanField(default=True, verbose_name='يتطلب تسجيل حضور')),
                ('requires_check_out', models.BooleanField(default=True, verbose_name='يتطلب تسجيل انصراف')),
                ('auto_check_out', models.BooleanField(default=False, help_text='تسجيل انصراف تلقائي في نهاية الوردية', verbose_name='تسجيل انصراف تلقائي')),
                ('minimum_hours_for_full_day', models.DecimalField(decimal_places=2, default=6.0, max_digits=4, verbose_name='الحد الأدنى للساعات ليوم كامل')),
                ('minimum_hours_for_half_day', models.DecimalField(decimal_places=2, default=4.0, max_digits=4, verbose_name='الحد الأدنى للساعات لنصف يوم')),
                ('color_code', models.CharField(default='#007bff', help_text='لون الوردية في التقويم والجداول', max_length=7, verbose_name='رمز اللون')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_default', models.BooleanField(default=False, help_text='هل هذه الوردية الافتراضية للموظفين الجدد؟', verbose_name='افتراضي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'وردية عمل',
                'verbose_name_plural': 'ورديات العمل',
                'db_table': 'hrms_work_shift',
                'ordering': ['start_time'],
            },
        ),
        migrations.RemoveField(
            model_name='employeeattendancerule',
            name='attendance_rule',
        ),
        migrations.RemoveField(
            model_name='employeeattendancerule',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='employeeevaluation',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='employeeevaluation',
            name='evaluator',
        ),
        migrations.RemoveField(
            model_name='employeefile',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='employeefile',
            name='uploaded_by',
        ),
        migrations.RemoveField(
            model_name='employeeleave',
            name='approved_by',
        ),
        migrations.RemoveField(
            model_name='employeeleave',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='employeeleave',
            name='leave_type',
        ),
        migrations.RemoveField(
            model_name='employeenote',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='employeenote',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='employeenote',
            name='last_modified_by',
        ),
        migrations.RemoveField(
            model_name='employeenotehistory',
            name='note',
        ),
        migrations.RemoveField(
            model_name='employeenotehistory',
            name='changed_by',
        ),
        migrations.RemoveField(
            model_name='employeesalaryitem',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='employeesalaryitem',
            name='salary_item',
        ),
        migrations.RemoveField(
            model_name='hrtask',
            name='assigned_to',
        ),
        migrations.RemoveField(
            model_name='hrtask',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='job',
            name='department',
        ),
        migrations.DeleteModel(
            name='JobInsurance',
        ),
        migrations.DeleteModel(
            name='OfficialHoliday',
        ),
        migrations.AlterUniqueTogether(
            name='payrollentry',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='period',
        ),
        migrations.RemoveField(
            model_name='payrollitemdetail',
            name='payroll_entry',
        ),
        migrations.RemoveField(
            model_name='payrollitemdetail',
            name='salary_item',
        ),
        migrations.AlterUniqueTogether(
            name='payrollperiod',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='payrollperiod',
            name='approved_by',
        ),
        migrations.RemoveField(
            model_name='payrollperiod',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='pickuppoint',
            name='car',
        ),
        migrations.AlterModelOptions(
            name='attendancemachine',
            options={'ordering': ['branch', 'name'], 'verbose_name': 'جهاز حضور', 'verbose_name_plural': 'أجهزة الحضور'},
        ),
        migrations.AlterModelOptions(
            name='attendancerecord',
            options={'ordering': ['-record_datetime'], 'verbose_name': 'سجل حضور', 'verbose_name_plural': 'سجلات الحضور'},
        ),
        migrations.AlterModelOptions(
            name='attendancesummary',
            options={'ordering': ['-date', 'employee'], 'verbose_name': 'ملخص حضور', 'verbose_name_plural': 'ملخصات الحضور'},
        ),
        migrations.AlterModelOptions(
            name='department',
            options={'ordering': ['company', 'level', 'name'], 'verbose_name': 'قسم', 'verbose_name_plural': 'الأقسام'},
        ),
        migrations.AlterModelOptions(
            name='employee',
            options={'ordering': ['employee_number'], 'verbose_name': 'موظف', 'verbose_name_plural': 'الموظفون'},
        ),
        migrations.AlterModelOptions(
            name='leavetype',
            options={'ordering': ['sort_order', 'name'], 'verbose_name': 'نوع إجازة', 'verbose_name_plural': 'أنواع الإجازات'},
        ),
        migrations.RenameField(
            model_name='attendancesummary',
            old_name='early_leave_minutes',
            new_name='early_departure_minutes',
        ),
        migrations.RenameField(
            model_name='attendancesummary',
            old_name='late_minutes',
            new_name='late_arrival_minutes',
        ),
        migrations.RemoveField(
            model_name='attendancemachine',
            name='location',
        ),
        migrations.RemoveField(
            model_name='attendancemachine',
            name='machine_type',
        ),
        migrations.RemoveField(
            model_name='attendancesummary',
            name='overtime_minutes',
        ),
        migrations.RemoveField(
            model_name='attendancesummary',
            name='time_in',
        ),
        migrations.RemoveField(
            model_name='attendancesummary',
            name='time_out',
        ),
        migrations.RemoveField(
            model_name='attendancesummary',
            name='working_minutes',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='age',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='birth_certificate',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='car_pick_up_point',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='car_ride_time',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='confirm_exit_insurance',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='confirmation_insurance_entry',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='contract_expiry_date',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='contract_renewal_date',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='contract_renewal_month',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='criminal_record_check',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='currentweekshift',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='date_birth',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='date_insurance_start',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='date_resignation',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='delivery_date_s1',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='delivery_date_s6',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='dept_name',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='due_insurance_amount',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_address',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_car',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_date_hiring',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_first_name',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_full_name',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_id',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_image',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_marital_status',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_name_english',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_nationality',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_phone1',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_phone2',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_second_name',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='emp_type',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='employment_contract',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='end_date_probationary_period',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='entrance_date_s1',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='entrance_date_s6',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='entrance_number_s1',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='entrance_number_s6',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='form_s1',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='form_s6',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='friday_operation',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='governorate',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='health_card',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='health_card_expiration_date',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='health_card_number',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='health_card_renewal_date',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='health_card_start_date',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='heel_work_number',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='heel_work_recipient',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='heel_work_recipient_address',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='heel_work_registration_date',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='hiring_date_health_card',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='id_card_photo',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='insurance_code',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='insurance_printout',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='insurance_salary',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='insurance_status',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='jop_code',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='jop_code_insurance',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='jop_name',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='jop_name_insurance',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='medical_exam_form',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='medical_exam_form_submission',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='military_service_certificate',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='mother_name',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='nextweekshift',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='number_insurance',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='people_with_special_needs',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='percentage_insurance_payable',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='personal_id_expiry_date',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='personal_photos',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='place_birth',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='qualification_certificate',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='reason_resignation',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='receive_date_s1',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='receive_date_s6',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='remaining_contract_renewal',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='shift_paper',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='shift_type',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='skill_level_measurement_certificate',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='social_status_report',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='the_health_card_remains_expire',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='work_heel',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='working_condition',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='years_since_contract_start',
        ),
        migrations.RemoveField(
            model_name='leavetype',
            name='affects_salary',
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='auto_sync_enabled',
            field=models.BooleanField(default=True, verbose_name='المزامنة التلقائية مفعلة'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_attendance_machines', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='current_records',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد السجلات الحالي'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='current_users',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد المستخدمين الحالي'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='device_id',
            field=models.CharField(default=1, max_length=50, unique=True, verbose_name='معرف الجهاز'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='device_settings',
            field=models.JSONField(default=dict, help_text='إعدادات خاصة بالجهاز', verbose_name='إعدادات الجهاز'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='device_type',
            field=models.CharField(choices=[('fingerprint', 'بصمة الإصبع'), ('face', 'التعرف على الوجه'), ('card', 'بطاقة'), ('password', 'كلمة مرور'), ('mixed', 'مختلط')], default=1, max_length=20, verbose_name='نوع الجهاز'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='firmware_version',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='إصدار البرنامج الثابت'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='floor',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='الطابق'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='installation_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ التركيب'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='is_entry_device',
            field=models.BooleanField(default=True, help_text='هل يستخدم هذا الجهاز لتسجيل الدخول؟', verbose_name='جهاز دخول'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='is_exit_device',
            field=models.BooleanField(default=True, help_text='هل يستخدم هذا الجهاز لتسجيل الخروج؟', verbose_name='جهاز خروج'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='last_maintenance_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ آخر صيانة'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='last_online_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='آخر وقت اتصال'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='last_sync_status',
            field=models.CharField(blank=True, choices=[('success', 'نجحت'), ('failed', 'فشلت'), ('partial', 'جزئية'), ('pending', 'معلقة')], max_length=20, null=True, verbose_name='حالة آخر مزامنة'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='last_sync_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='آخر وقت مزامنة'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='location_description',
            field=models.CharField(default=1, help_text='مثل: المدخل الرئيسي، مدخل الموظفين، إلخ', max_length=200, verbose_name='وصف الموقع'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='maintenance_notes',
            field=models.TextField(blank=True, null=True, verbose_name='ملاحظات الصيانة'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='manufacturer',
            field=models.CharField(default='ZKTeco', max_length=100, verbose_name='الشركة المصنعة'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='max_fingerprints',
            field=models.PositiveIntegerField(default=2000, verbose_name='الحد الأقصى للبصمات'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='max_records',
            field=models.PositiveIntegerField(default=100000, verbose_name='الحد الأقصى للسجلات'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='max_users',
            field=models.PositiveIntegerField(default=1000, verbose_name='الحد الأقصى للمستخدمين'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='model',
            field=models.CharField(default=1, max_length=100, verbose_name='موديل الجهاز'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='next_maintenance_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ الصيانة القادمة'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='password',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='كلمة مرور الجهاز'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='platform',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='المنصة'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='serial_number',
            field=models.CharField(default=1, max_length=100, unique=True, verbose_name='الرقم التسلسلي'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='status',
            field=models.CharField(choices=[('online', 'متصل'), ('offline', 'غير متصل'), ('error', 'خطأ'), ('maintenance', 'صيانة'), ('disabled', 'معطل')], default='offline', max_length=20, verbose_name='حالة الجهاز'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='supports_card',
            field=models.BooleanField(default=False, verbose_name='يدعم البطاقة'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='supports_face_recognition',
            field=models.BooleanField(default=False, verbose_name='يدعم التعرف على الوجه'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='supports_fingerprint',
            field=models.BooleanField(default=True, verbose_name='يدعم بصمة الإصبع'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='supports_password',
            field=models.BooleanField(default=False, verbose_name='يدعم كلمة المرور'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='sync_interval_minutes',
            field=models.PositiveIntegerField(default=15, verbose_name='فترة المزامنة (دقائق)'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='warranty_expiry_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الضمان'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_attendance_records', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_attendance_records', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='device_user_id',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='معرف المستخدم في الجهاز'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='early_minutes',
            field=models.PositiveIntegerField(default=0, verbose_name='دقائق التبكير'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='is_early',
            field=models.BooleanField(default=False, verbose_name='مبكر'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='is_edited',
            field=models.BooleanField(default=False, verbose_name='تم التعديل'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='is_late',
            field=models.BooleanField(default=False, verbose_name='متأخر'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='is_manual_entry',
            field=models.BooleanField(default=False, verbose_name='إدخال يدوي'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='is_processed',
            field=models.BooleanField(default=False, verbose_name='تم المعالجة'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='late_minutes',
            field=models.PositiveIntegerField(default=0, verbose_name='دقائق التأخير'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='location_description',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='وصف الموقع'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='raw_data',
            field=models.JSONField(default=dict, help_text='البيانات الأصلية من الجهاز', verbose_name='البيانات الخام'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='record_datetime',
            field=models.DateTimeField(default=1, verbose_name='تاريخ ووقت التسجيل'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='requires_approval',
            field=models.BooleanField(default=False, verbose_name='يتطلب موافقة'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='status',
            field=models.CharField(choices=[('valid', 'صحيح'), ('invalid', 'غير صحيح'), ('duplicate', 'مكرر'), ('late', 'متأخر'), ('early', 'مبكر'), ('pending', 'معلق'), ('processed', 'تم معالجته')], default='valid', max_length=15, verbose_name='حالة التسجيل'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='verification_method',
            field=models.CharField(choices=[('fingerprint', 'بصمة الإصبع'), ('face', 'التعرف على الوجه'), ('card', 'بطاقة'), ('password', 'كلمة مرور'), ('manual', 'يدوي'), ('unknown', 'غير معروف')], default='fingerprint', max_length=15, verbose_name='طريقة التحقق'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_attendance_summaries', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='break_hours',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='ساعات الراحة'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='calculation_details',
            field=models.JSONField(default=dict, help_text='تفاصيل حساب ساعات العمل', verbose_name='تفاصيل الحساب'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='exceptions',
            field=models.JSONField(default=list, help_text='قائمة بالاستثناءات والمشاكل', verbose_name='الاستثناءات'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='first_in_time',
            field=models.TimeField(blank=True, null=True, verbose_name='أول وقت دخول'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='has_incomplete_punches',
            field=models.BooleanField(default=False, verbose_name='تسجيلات ناقصة'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='in_punches',
            field=models.PositiveIntegerField(default=0, verbose_name='تسجيلات الدخول'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='is_holiday',
            field=models.BooleanField(default=False, verbose_name='عطلة رسمية'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='is_processed',
            field=models.BooleanField(default=False, verbose_name='تم المعالجة'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='is_weekend',
            field=models.BooleanField(default=False, verbose_name='عطلة أسبوعية'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='is_working_day',
            field=models.BooleanField(default=True, verbose_name='يوم عمل'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='last_out_time',
            field=models.TimeField(blank=True, null=True, verbose_name='آخر وقت خروج'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='leave_hours',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='ساعات الإجازة'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='out_punches',
            field=models.PositiveIntegerField(default=0, verbose_name='تسجيلات الخروج'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='overtime_hours',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='ساعات العمل الإضافي'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='processed_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ المعالجة'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='regular_hours',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='الساعات العادية'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='requires_approval',
            field=models.BooleanField(default=False, verbose_name='يتطلب موافقة'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='scheduled_in_time',
            field=models.TimeField(blank=True, null=True, verbose_name='وقت الدخول المجدول'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='scheduled_out_time',
            field=models.TimeField(blank=True, null=True, verbose_name='وقت الخروج المجدول'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='total_punches',
            field=models.PositiveIntegerField(default=0, verbose_name='إجمالي التسجيلات'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='total_work_hours',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='إجمالي ساعات العمل'),
        ),
        migrations.AddField(
            model_name='department',
            name='annual_goals',
            field=models.JSONField(default=list, help_text='قائمة بالأهداف السنوية للقسم', verbose_name='الأهداف السنوية'),
        ),
        migrations.AddField(
            model_name='department',
            name='assistant_manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assistant_managed_departments', to='Hr.employee', verbose_name='مساعد مدير القسم'),
        ),
        migrations.AddField(
            model_name='department',
            name='budget',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الميزانية السنوية'),
        ),
        migrations.AddField(
            model_name='department',
            name='code',
            field=models.CharField(default='DEPT001', help_text='كود فريد للقسم', max_length=20, unique=True, validators=[django.core.validators.RegexValidator(message='كود القسم يجب أن يحتوي على أحرف كبيرة وأرقام فقط', regex='^[A-Z0-9\\-]+$')], verbose_name='كود القسم'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='department',
            name='cost_center_code',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='كود مركز التكلفة'),
        ),
        migrations.AddField(
            model_name='department',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='department',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_departments', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='department',
            name='department_settings',
            field=models.JSONField(default=dict, help_text='إعدادات خاصة بالقسم', verbose_name='إعدادات القسم'),
        ),
        migrations.AddField(
            model_name='department',
            name='department_type',
            field=models.CharField(choices=[('operational', 'تشغيلي'), ('administrative', 'إداري'), ('support', 'دعم'), ('technical', 'تقني'), ('sales', 'مبيعات'), ('finance', 'مالي'), ('hr', 'موارد بشرية'), ('it', 'تكنولوجيا المعلومات'), ('marketing', 'تسويق'), ('production', 'إنتاج'), ('quality', 'جودة'), ('research', 'بحث وتطوير')], default='operational', max_length=20, verbose_name='نوع القسم'),
        ),
        migrations.AddField(
            model_name='department',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='وصف القسم'),
        ),
        migrations.AddField(
            model_name='department',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني'),
        ),
        migrations.AddField(
            model_name='department',
            name='employee_capacity',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='السعة القصوى للموظفين'),
        ),
        migrations.AddField(
            model_name='department',
            name='established_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ التأسيس'),
        ),
        migrations.AddField(
            model_name='department',
            name='extension',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='رقم التحويلة'),
        ),
        migrations.AddField(
            model_name='department',
            name='floor',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='الطابق'),
        ),
        migrations.AddField(
            model_name='department',
            name='id',
            field=models.BigAutoField(auto_created=True, default=1, primary_key=True, serialize=False, verbose_name='ID'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='department',
            name='kpis',
            field=models.JSONField(default=list, help_text='مؤشرات الأداء الرئيسية للقسم', verbose_name='مؤشرات الأداء الرئيسية'),
        ),
        migrations.AddField(
            model_name='department',
            name='level',
            field=models.PositiveIntegerField(default=1, help_text='مستوى القسم في الهيكل التنظيمي', verbose_name='مستوى القسم'),
        ),
        migrations.AddField(
            model_name='department',
            name='location_notes',
            field=models.TextField(blank=True, null=True, verbose_name='ملاحظات الموقع'),
        ),
        migrations.AddField(
            model_name='department',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='Hr.employee', verbose_name='مدير القسم'),
        ),
        migrations.AddField(
            model_name='department',
            name='name',
            field=models.CharField(default='قسم عام', max_length=200, verbose_name='اسم القسم'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='department',
            name='parent_department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sub_departments', to='Hr.department', verbose_name='القسم الأب'),
        ),
        migrations.AddField(
            model_name='department',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف'),
        ),
        migrations.AddField(
            model_name='department',
            name='room_number',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم المكتب'),
        ),
        migrations.AddField(
            model_name='department',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AddField(
            model_name='department',
            name='working_hours_end',
            field=models.TimeField(blank=True, null=True, verbose_name='نهاية ساعات العمل'),
        ),
        migrations.AddField(
            model_name='department',
            name='working_hours_start',
            field=models.TimeField(blank=True, null=True, verbose_name='بداية ساعات العمل'),
        ),
        migrations.AddField(
            model_name='employee',
            name='address',
            field=models.TextField(blank=True, null=True, verbose_name='العنوان'),
        ),
        migrations.AddField(
            model_name='employee',
            name='bank_account_number',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحساب البنكي'),
        ),
        migrations.AddField(
            model_name='employee',
            name='bank_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم البنك'),
        ),
        migrations.AddField(
            model_name='employee',
            name='basic_salary',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='الراتب الأساسي'),
        ),
        migrations.AddField(
            model_name='employee',
            name='birth_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد'),
        ),
        migrations.AddField(
            model_name='employee',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='المدينة'),
        ),
        migrations.AddField(
            model_name='employee',
            name='confirmation_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ التثبيت'),
        ),
        migrations.AddField(
            model_name='employee',
            name='country',
            field=models.CharField(default='مصر', max_length=100, verbose_name='الدولة'),
        ),
        migrations.AddField(
            model_name='employee',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employee',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employees', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employee',
            name='currency',
            field=models.CharField(default='EGP', max_length=3, verbose_name='العملة'),
        ),
        migrations.AddField(
            model_name='employee',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True, validators=[django.core.validators.EmailValidator()], verbose_name='البريد الإلكتروني'),
        ),
        migrations.AddField(
            model_name='employee',
            name='emergency_contact_name',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم جهة الاتصال في حالات الطوارئ'),
        ),
        migrations.AddField(
            model_name='employee',
            name='emergency_contact_phone',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='هاتف جهة الاتصال في حالات الطوارئ'),
        ),
        migrations.AddField(
            model_name='employee',
            name='emergency_contact_relationship',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='صلة القرابة'),
        ),
        migrations.AddField(
            model_name='employee',
            name='employee_number',
            field=models.CharField(default='EMP001', help_text='رقم فريد للموظف', max_length=20, unique=True, validators=[django.core.validators.RegexValidator(message='رقم الموظف يجب أن يحتوي على أحرف كبيرة وأرقام فقط', regex='^[A-Z0-9\\-]+$')], verbose_name='رقم الموظف'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employee',
            name='employment_type',
            field=models.CharField(choices=[('full_time', 'دوام كامل'), ('part_time', 'دوام جزئي'), ('contract', 'تعاقد'), ('temporary', 'مؤقت'), ('intern', 'متدرب'), ('consultant', 'استشاري')], default='full_time', max_length=20, verbose_name='نوع التوظيف'),
        ),
        migrations.AddField(
            model_name='employee',
            name='first_name',
            field=models.CharField(default='موظف', max_length=100, verbose_name='الاسم الأول'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employee',
            name='full_name',
            field=models.CharField(blank=True, max_length=300, null=True, verbose_name='الاسم الكامل'),
        ),
        migrations.AddField(
            model_name='employee',
            name='full_name_english',
            field=models.CharField(blank=True, max_length=300, null=True, verbose_name='الاسم الكامل بالإنجليزية'),
        ),
        migrations.AddField(
            model_name='employee',
            name='gender',
            field=models.CharField(blank=True, choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, null=True, verbose_name='الجنس'),
        ),
        migrations.AddField(
            model_name='employee',
            name='hire_date',
            field=models.DateField(default='2025-01-01', verbose_name='تاريخ التوظيف'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employee',
            name='iban',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الآيبان'),
        ),
        migrations.AddField(
            model_name='employee',
            name='id',
            field=models.BigAutoField(auto_created=True, default=1, primary_key=True, serialize=False, verbose_name='ID'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employee',
            name='last_name',
            field=models.CharField(default='جديد', max_length=100, verbose_name='اسم العائلة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employee',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subordinates', to='Hr.employee', verbose_name='المدير المباشر'),
        ),
        migrations.AddField(
            model_name='employee',
            name='marital_status',
            field=models.CharField(blank=True, choices=[('single', 'أعزب'), ('married', 'متزوج'), ('divorced', 'مطلق'), ('widowed', 'أرمل')], max_length=20, null=True, verbose_name='الحالة الاجتماعية'),
        ),
        migrations.AddField(
            model_name='employee',
            name='middle_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='الاسم الأوسط'),
        ),
        migrations.AddField(
            model_name='employee',
            name='mobile',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الجوال'),
        ),
        migrations.AddField(
            model_name='employee',
            name='nationality',
            field=models.CharField(default='مصري', max_length=50, verbose_name='الجنسية'),
        ),
        migrations.AddField(
            model_name='employee',
            name='notes',
            field=models.TextField(blank=True, null=True, verbose_name='ملاحظات'),
        ),
        migrations.AddField(
            model_name='employee',
            name='passport_expiry',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء جواز السفر'),
        ),
        migrations.AddField(
            model_name='employee',
            name='passport_number',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم جواز السفر'),
        ),
        migrations.AddField(
            model_name='employee',
            name='personal_email',
            field=models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني الشخصي'),
        ),
        migrations.AddField(
            model_name='employee',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف'),
        ),
        migrations.AddField(
            model_name='employee',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='الرمز البريدي'),
        ),
        migrations.AddField(
            model_name='employee',
            name='probation_end_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء فترة التجربة'),
        ),
        migrations.AddField(
            model_name='employee',
            name='profile_picture',
            field=models.ImageField(blank=True, null=True, upload_to='employee_profiles/', verbose_name='صورة شخصية'),
        ),
        migrations.AddField(
            model_name='employee',
            name='religion',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='الديانة'),
        ),
        migrations.AddField(
            model_name='employee',
            name='state',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='المحافظة/الولاية'),
        ),
        migrations.AddField(
            model_name='employee',
            name='status',
            field=models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('on_leave', 'في إجازة'), ('suspended', 'موقوف'), ('terminated', 'منتهي الخدمة'), ('resigned', 'مستقيل')], default='active', max_length=20, verbose_name='حالة الموظف'),
        ),
        migrations.AddField(
            model_name='employee',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AddField(
            model_name='employee',
            name='user_account',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_profile', to=settings.AUTH_USER_MODEL, verbose_name='حساب المستخدم'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='accrual_method',
            field=models.CharField(choices=[('annual', 'سنوي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي'), ('weekly', 'أسبوعي'), ('none', 'لا يوجد استحقاق')], default='annual', max_length=20, verbose_name='طريقة الاستحقاق'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='accrual_rate',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='عدد الأيام المستحقة حسب طريقة الاستحقاق', max_digits=5, null=True, verbose_name='معدل الاستحقاق'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='approval_levels',
            field=models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='مستويات الموافقة'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='auto_approve_threshold',
            field=models.PositiveIntegerField(blank=True, help_text='الطلبات أقل من هذا العدد تتم الموافقة عليها تلقائياً', null=True, verbose_name='حد الموافقة التلقائية (أيام)'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='blackout_periods',
            field=models.JSONField(default=list, help_text='فترات لا يمكن أخذ إجازة فيها', verbose_name='فترات المنع'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='can_be_split',
            field=models.BooleanField(default=True, help_text='هل يمكن أخذ هذه الإجازة على فترات منفصلة؟', verbose_name='يمكن تقسيمها'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='carry_forward_allowed',
            field=models.BooleanField(default=False, help_text='هل يمكن ترحيل الرصيد للسنة التالية؟', verbose_name='يسمح بترحيل الرصيد'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='carry_forward_expiry_months',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='انتهاء صلاحية الرصيد المرحل (شهور)'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='category',
            field=models.CharField(choices=[('annual', 'إجازة سنوية'), ('sick', 'إجازة مرضية'), ('maternity', 'إجازة أمومة'), ('paternity', 'إجازة أبوة'), ('emergency', 'إجازة طارئة'), ('bereavement', 'إجازة وفاة'), ('study', 'إجازة دراسية'), ('pilgrimage', 'إجازة حج/عمرة'), ('marriage', 'إجازة زواج'), ('unpaid', 'إجازة بدون راتب'), ('compensatory', 'إجازة تعويضية'), ('sabbatical', 'إجازة تفرغ'), ('military', 'إجازة خدمة عسكرية'), ('other', 'أخرى')], default='annual', max_length=20, verbose_name='فئة الإجازة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='leavetype',
            name='code',
            field=models.CharField(default='LEAVE001', max_length=20, unique=True, verbose_name='كود نوع الإجازة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='leavetype',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_leave_types', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='exclude_holidays',
            field=models.BooleanField(default=True, verbose_name='استبعاد العطل الرسمية'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='exclude_weekends',
            field=models.BooleanField(default=True, verbose_name='استبعاد عطلات نهاية الأسبوع'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='gender_eligibility',
            field=models.CharField(choices=[('all', 'الجميع'), ('male', 'ذكور فقط'), ('female', 'إناث فقط')], default='all', max_length=10, verbose_name='الأهلية حسب الجنس'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='is_system_leave',
            field=models.BooleanField(default=False, help_text='هل هذا نوع إجازة أساسي في النظام؟', verbose_name='إجازة نظام'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='leave_settings',
            field=models.JSONField(default=dict, help_text='إعدادات إضافية خاصة بنوع الإجازة', verbose_name='إعدادات الإجازة'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='max_carry_forward_days',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='الحد الأقصى لأيام الترحيل'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='max_concurrent_employees',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='الحد الأقصى للموظفين في نفس الوقت'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='max_days_per_request',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='الحد الأقصى للأيام في الطلب الواحد'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='maximum_advance_days',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='الحد الأقصى للحجز المسبق (أيام)'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='medical_certificate_threshold',
            field=models.PositiveIntegerField(blank=True, help_text='عدد الأيام التي تتطلب شهادة طبية', null=True, verbose_name='حد الشهادة الطبية (أيام)'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='min_days_per_request',
            field=models.PositiveIntegerField(default=1, verbose_name='الحد الأدنى للأيام في الطلب الواحد'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='minimum_notice_days',
            field=models.PositiveIntegerField(default=1, verbose_name='الحد الأدنى لفترة الإشعار (أيام)'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='minimum_service_months',
            field=models.PositiveIntegerField(default=0, verbose_name='الحد الأدنى لشهور الخدمة'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='payment_percentage',
            field=models.DecimalField(decimal_places=2, default=100.0, help_text='نسبة الراتب المدفوع أثناء الإجازة', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة الدفع (%)'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='requires_approval',
            field=models.BooleanField(default=True, verbose_name='يتطلب موافقة'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='requires_medical_certificate',
            field=models.BooleanField(default=False, verbose_name='يتطلب شهادة طبية'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='requires_supporting_documents',
            field=models.BooleanField(default=False, verbose_name='يتطلب مستندات داعمة'),
        ),
        migrations.AddField(
            model_name='leavetype',
            name='sort_order',
            field=models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض'),
        ),
        migrations.AlterField(
            model_name='attendancemachine',
            name='ip_address',
            field=models.GenericIPAddressField(validators=[django.core.validators.RegexValidator(message='عنوان IP غير صحيح', regex='^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$')], verbose_name='عنوان IP'),
        ),
        migrations.AlterField(
            model_name='attendancemachine',
            name='name',
            field=models.CharField(max_length=100, verbose_name='اسم الجهاز'),
        ),
        migrations.AlterField(
            model_name='attendancemachine',
            name='port',
            field=models.PositiveIntegerField(default=4370, verbose_name='رقم المنفذ'),
        ),
        migrations.AlterField(
            model_name='attendancerecord',
            name='machine',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='attendance_records', to='Hr.attendancemachine', verbose_name='جهاز الحضور'),
        ),
        migrations.AlterField(
            model_name='attendancerecord',
            name='record_type',
            field=models.CharField(choices=[('in', 'دخول'), ('out', 'خروج'), ('break_out', 'خروج للراحة'), ('break_in', 'عودة من الراحة'), ('overtime_in', 'دخول عمل إضافي'), ('overtime_out', 'خروج عمل إضافي')], max_length=15, verbose_name='نوع التسجيل'),
        ),
        migrations.AlterField(
            model_name='attendancerecord',
            name='source',
            field=models.CharField(choices=[('machine', 'جهاز حضور'), ('manual', 'يدوي'), ('mobile', 'تطبيق جوال'), ('web', 'موقع ويب'), ('import', 'استيراد'), ('system', 'نظام')], default='machine', max_length=10, verbose_name='مصدر التسجيل'),
        ),
        migrations.AlterField(
            model_name='attendancesummary',
            name='status',
            field=models.CharField(choices=[('present', 'حاضر'), ('absent', 'غائب'), ('late', 'متأخر'), ('early_leave', 'انصراف مبكر'), ('half_day', 'نصف يوم'), ('on_leave', 'في إجازة'), ('holiday', 'عطلة رسمية'), ('weekend', 'عطلة أسبوعية'), ('sick', 'مريض'), ('training', 'تدريب'), ('business_trip', 'مهمة عمل'), ('no_show', 'لم يحضر')], max_length=20, verbose_name='حالة الحضور'),
        ),
        migrations.AlterField(
            model_name='employee',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employees', to='Hr.department', verbose_name='القسم'),
        ),
        migrations.AlterField(
            model_name='employee',
            name='national_id',
            field=models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='رقم الهوية الوطنية'),
        ),
        migrations.AlterField(
            model_name='leavetype',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='وصف نوع الإجازة'),
        ),
        migrations.AlterField(
            model_name='leavetype',
            name='is_paid',
            field=models.BooleanField(default=True, verbose_name='إجازة مدفوعة الأجر'),
        ),
        migrations.AlterField(
            model_name='leavetype',
            name='max_days_per_year',
            field=models.PositiveIntegerField(blank=True, help_text='الحد الأقصى لأيام الإجازة في السنة', null=True, verbose_name='الحد الأقصى للأيام سنوياً'),
        ),
        migrations.AlterField(
            model_name='leavetype',
            name='name',
            field=models.CharField(max_length=100, verbose_name='اسم نوع الإجازة'),
        ),
        migrations.AlterUniqueTogether(
            name='attendancerecord',
            unique_together={('employee', 'record_datetime', 'record_type', 'machine')},
        ),
        migrations.AddIndex(
            model_name='leavetype',
            index=models.Index(fields=['code'], name='hrms_leave__code_e057b2_idx'),
        ),
        migrations.AddIndex(
            model_name='leavetype',
            index=models.Index(fields=['category'], name='hrms_leave__categor_3ad6a1_idx'),
        ),
        migrations.AddIndex(
            model_name='leavetype',
            index=models.Index(fields=['is_active'], name='hrms_leave__is_acti_fe50e4_idx'),
        ),
        migrations.AddIndex(
            model_name='leavetype',
            index=models.Index(fields=['sort_order'], name='hrms_leave__sort_or_e0c269_idx'),
        ),
        migrations.AlterModelTable(
            name='attendancemachine',
            table='hrms_attendance_machine',
        ),
        migrations.AlterModelTable(
            name='attendancerecord',
            table='hrms_attendance_record',
        ),
        migrations.AlterModelTable(
            name='attendancesummary',
            table='hrms_attendance_summary',
        ),
        migrations.AlterModelTable(
            name='department',
            table='hrms_department',
        ),
        migrations.AlterModelTable(
            name='employee',
            table='hrms_employee',
        ),
        migrations.AlterModelTable(
            name='leavetype',
            table='hrms_leave_type',
        ),
        migrations.AddField(
            model_name='branch',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_branches', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='branch',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_branches', to='Hr.employee', verbose_name='مدير الفرع'),
        ),
        migrations.AddField(
            model_name='attendancemachine',
            name='branch',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='attendance_machines', to='Hr.branch', verbose_name='الفرع'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='department',
            name='branch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='departments', to='Hr.branch', verbose_name='الفرع'),
        ),
        migrations.AddField(
            model_name='employee',
            name='branch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employees', to='Hr.branch', verbose_name='الفرع'),
        ),
        migrations.AddIndex(
            model_name='attendancemachine',
            index=models.Index(fields=['device_id'], name='hrms_attend_device__2463d7_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancemachine',
            index=models.Index(fields=['ip_address'], name='hrms_attend_ip_addr_e458a5_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancemachine',
            index=models.Index(fields=['status'], name='hrms_attend_status_784a25_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancemachine',
            index=models.Index(fields=['branch'], name='hrms_attend_branch__f98d05_idx'),
        ),
        migrations.AddField(
            model_name='company',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_companies', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='branch',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branches', to='Hr.company', verbose_name='الشركة'),
        ),
        migrations.AddField(
            model_name='department',
            name='company',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='departments', to='Hr.company', verbose_name='الشركة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employee',
            name='company',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='Hr.company', verbose_name='الشركة'),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name='department',
            unique_together={('company', 'code')},
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['company', 'name'], name='hrms_depart_company_73ee3e_idx'),
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['code'], name='hrms_depart_code_c1d40b_idx'),
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['is_active'], name='hrms_depart_is_acti_afdeaa_idx'),
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['department_type'], name='hrms_depart_departm_dcb9b2_idx'),
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['level'], name='hrms_depart_level_c46999_idx'),
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='replaced_document',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='replacement_documents', to='Hr.employeedocument', verbose_name='الوثيقة المستبدلة'),
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='uploaded_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_documents', to=settings.AUTH_USER_MODEL, verbose_name='رفع بواسطة'),
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='verified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_documents', to=settings.AUTH_USER_MODEL, verbose_name='تم التحقق بواسطة'),
        ),
        migrations.AddField(
            model_name='employeeemergencycontact',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_emergency_contacts', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employeeemergencycontact',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='emergency_contacts', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeeshiftassignment',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_shift_assignments', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة'),
        ),
        migrations.AddField(
            model_name='employeeshiftassignment',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_shift_assignments', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employeeshiftassignment',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shift_assignments', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeetraining',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_trainings', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة'),
        ),
        migrations.AddField(
            model_name='employeetraining',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_trainings', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employeetraining',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trainings', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='jobposition',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_job_positions', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='jobposition',
            name='department',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_positions', to='Hr.department', verbose_name='القسم'),
        ),
        migrations.AddField(
            model_name='jobposition',
            name='reports_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subordinate_positions', to='Hr.jobposition', verbose_name='يرفع تقارير إلى'),
        ),
        migrations.AddField(
            model_name='employee',
            name='job_position',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employees', to='Hr.jobposition', verbose_name='الوظيفة'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['employee_number'], name='hrms_employ_employe_78f709_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['email'], name='hrms_employ_email_f94efb_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['national_id'], name='hrms_employ_nationa_dc235e_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['status'], name='hrms_employ_status_1d0e6b_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['company', 'department'], name='hrms_employ_company_091840_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['hire_date'], name='hrms_employ_hire_da_7d8095_idx'),
        ),
        migrations.AddField(
            model_name='leaveapproval',
            name='approved_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_approvals', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة'),
        ),
        migrations.AddField(
            model_name='leavebalance',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_balances', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='leavebalance',
            name='leave_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_balances', to='Hr.leavetype', verbose_name='نوع الإجازة'),
        ),
        migrations.AddField(
            model_name='leavepolicy',
            name='branches',
            field=models.ManyToManyField(blank=True, help_text='الفروع التي تطبق عليها هذه السياسة', related_name='leave_policies', to='Hr.branch', verbose_name='الفروع'),
        ),
        migrations.AddField(
            model_name='leavepolicy',
            name='company',
            field=models.ForeignKey(blank=True, help_text='إذا لم يتم تحديد شركة، ستطبق على جميع الشركات', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='leave_policies', to='Hr.company', verbose_name='الشركة'),
        ),
        migrations.AddField(
            model_name='leavepolicy',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_leave_policies', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='leavepolicy',
            name='departments',
            field=models.ManyToManyField(blank=True, help_text='الأقسام التي تطبق عليها هذه السياسة', related_name='leave_policies', to='Hr.department', verbose_name='الأقسام'),
        ),
        migrations.AddField(
            model_name='leavepolicy',
            name='job_positions',
            field=models.ManyToManyField(blank=True, help_text='الوظائف التي تطبق عليها هذه السياسة', related_name='leave_policies', to='Hr.jobposition', verbose_name='الوظائف'),
        ),
        migrations.AddField(
            model_name='leaverequest',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='leaverequest',
            name='handover_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='received_handovers', to='Hr.employee', verbose_name='تسليم المهام إلى'),
        ),
        migrations.AddField(
            model_name='leaverequest',
            name='leave_policy',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='leave_requests', to='Hr.leavepolicy', verbose_name='سياسة الإجازة'),
        ),
        migrations.AddField(
            model_name='leaverequest',
            name='leave_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to='Hr.leavetype', verbose_name='نوع الإجازة'),
        ),
        migrations.AddField(
            model_name='leaverequest',
            name='return_confirmed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='confirmed_returns', to=settings.AUTH_USER_MODEL, verbose_name='تم تأكيد العودة بواسطة'),
        ),
        migrations.AddField(
            model_name='leaverequest',
            name='submitted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='submitted_leave_requests', to=settings.AUTH_USER_MODEL, verbose_name='قدم بواسطة'),
        ),
        migrations.AddField(
            model_name='leaveapproval',
            name='leave_request',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='approvals', to='Hr.leaverequest', verbose_name='طلب الإجازة'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='leave_request',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='attendance_summaries', to='Hr.leaverequest', verbose_name='طلب الإجازة'),
        ),
        migrations.AddField(
            model_name='leavetransaction',
            name='leave_balance',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='Hr.leavebalance', verbose_name='رصيد الإجازة'),
        ),
        migrations.AddField(
            model_name='leavetransaction',
            name='processed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='leave_transactions', to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة'),
        ),
        migrations.AddField(
            model_name='salarycomponent',
            name='basis_component',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dependent_components', to='Hr.salarycomponent', verbose_name='المكون الأساسي'),
        ),
        migrations.AddField(
            model_name='salarycomponent',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_salary_components', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='workshift',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_work_shifts', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employeeshiftassignment',
            name='work_shift',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_assignments', to='Hr.workshift', verbose_name='وردية العمل'),
        ),
        migrations.AddField(
            model_name='attendancerecord',
            name='work_shift',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='attendance_records', to='Hr.workshift', verbose_name='وردية العمل'),
        ),
        migrations.AddField(
            model_name='attendancesummary',
            name='work_shift',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='attendance_summaries', to='Hr.workshift', verbose_name='وردية العمل'),
        ),
        migrations.AddIndex(
            model_name='attendancerecord',
            index=models.Index(fields=['employee', 'record_date'], name='hrms_attend_employe_05d53e_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancerecord',
            index=models.Index(fields=['record_date', 'record_time'], name='hrms_attend_record__075871_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancerecord',
            index=models.Index(fields=['status'], name='hrms_attend_status_293f75_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancerecord',
            index=models.Index(fields=['record_type'], name='hrms_attend_record__f2def1_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancerecord',
            index=models.Index(fields=['machine'], name='hrms_attend_machine_8e4e85_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancesummary',
            index=models.Index(fields=['employee', 'date'], name='hrms_attend_employe_c71f86_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancesummary',
            index=models.Index(fields=['date'], name='hrms_attend_date_597201_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancesummary',
            index=models.Index(fields=['status'], name='hrms_attend_status_c26732_idx'),
        ),
        migrations.AddIndex(
            model_name='attendancesummary',
            index=models.Index(fields=['is_processed'], name='hrms_attend_is_proc_dd2215_idx'),
        ),
        migrations.DeleteModel(
            name='AttendanceRule',
        ),
        migrations.DeleteModel(
            name='EmployeeAttendanceRule',
        ),
        migrations.DeleteModel(
            name='EmployeeEvaluation',
        ),
        migrations.DeleteModel(
            name='EmployeeFile',
        ),
        migrations.DeleteModel(
            name='EmployeeLeave',
        ),
        migrations.DeleteModel(
            name='EmployeeNote',
        ),
        migrations.DeleteModel(
            name='EmployeeNoteHistory',
        ),
        migrations.DeleteModel(
            name='EmployeeSalaryItem',
        ),
        migrations.DeleteModel(
            name='HrTask',
        ),
        migrations.DeleteModel(
            name='Job',
        ),
        migrations.DeleteModel(
            name='PayrollEntry',
        ),
        migrations.DeleteModel(
            name='PayrollItemDetail',
        ),
        migrations.DeleteModel(
            name='SalaryItem',
        ),
        migrations.DeleteModel(
            name='PayrollPeriod',
        ),
        migrations.DeleteModel(
            name='PickupPoint',
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['name'], name='hrms_compan_name_e35078_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['tax_id'], name='hrms_compan_tax_id_1a9057_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['is_active'], name='hrms_compan_is_acti_263f67_idx'),
        ),
        migrations.AddIndex(
            model_name='branch',
            index=models.Index(fields=['company', 'name'], name='hrms_branch_company_8f4949_idx'),
        ),
        migrations.AddIndex(
            model_name='branch',
            index=models.Index(fields=['code'], name='hrms_branch_code_13d9ca_idx'),
        ),
        migrations.AddIndex(
            model_name='branch',
            index=models.Index(fields=['is_active'], name='hrms_branch_is_acti_f70c0d_idx'),
        ),
        migrations.AddIndex(
            model_name='branch',
            index=models.Index(fields=['city'], name='hrms_branch_city_a1d0dd_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='branch',
            unique_together={('company', 'code')},
        ),
        migrations.RemoveField(
            model_name='department',
            name='dept_code',
        ),
        migrations.RemoveField(
            model_name='department',
            name='dept_name',
        ),
        migrations.RemoveField(
            model_name='department',
            name='manager_id',
        ),
        migrations.RemoveField(
            model_name='department',
            name='note',
        ),
        migrations.AddIndex(
            model_name='employeedocument',
            index=models.Index(fields=['employee', 'document_type'], name='hrms_employ_employe_2b10a6_idx'),
        ),
        migrations.AddIndex(
            model_name='employeedocument',
            index=models.Index(fields=['status'], name='hrms_employ_status_55ba3e_idx'),
        ),
        migrations.AddIndex(
            model_name='employeedocument',
            index=models.Index(fields=['expiry_date'], name='hrms_employ_expiry__dcb8e6_idx'),
        ),
        migrations.AddIndex(
            model_name='employeedocument',
            index=models.Index(fields=['is_verified'], name='hrms_employ_is_veri_480801_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeemergencycontact',
            index=models.Index(fields=['employee', 'priority'], name='hrms_employ_employe_0e6341_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeemergencycontact',
            index=models.Index(fields=['is_primary'], name='hrms_employ_is_prim_a3d5c9_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeemergencycontact',
            index=models.Index(fields=['is_active'], name='hrms_employ_is_acti_eaf2a1_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='employeeemergencycontact',
            unique_together={('employee', 'priority')},
        ),
        migrations.AddIndex(
            model_name='employeetraining',
            index=models.Index(fields=['employee', 'start_date'], name='hrms_employ_employe_4deec8_idx'),
        ),
        migrations.AddIndex(
            model_name='employeetraining',
            index=models.Index(fields=['status'], name='hrms_employ_status_254ac5_idx'),
        ),
        migrations.AddIndex(
            model_name='employeetraining',
            index=models.Index(fields=['training_type'], name='hrms_employ_trainin_f27248_idx'),
        ),
        migrations.AddIndex(
            model_name='employeetraining',
            index=models.Index(fields=['certificate_expiry'], name='hrms_employ_certifi_5859f9_idx'),
        ),
        migrations.AddIndex(
            model_name='jobposition',
            index=models.Index(fields=['department', 'title'], name='hrms_job_po_departm_2b5f25_idx'),
        ),
        migrations.AddIndex(
            model_name='jobposition',
            index=models.Index(fields=['code'], name='hrms_job_po_code_0b8c07_idx'),
        ),
        migrations.AddIndex(
            model_name='jobposition',
            index=models.Index(fields=['level'], name='hrms_job_po_level_903103_idx'),
        ),
        migrations.AddIndex(
            model_name='jobposition',
            index=models.Index(fields=['employment_type'], name='hrms_job_po_employm_04a38e_idx'),
        ),
        migrations.AddIndex(
            model_name='jobposition',
            index=models.Index(fields=['is_active'], name='hrms_job_po_is_acti_7ffedf_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='jobposition',
            unique_together={('department', 'code')},
        ),
        migrations.AddIndex(
            model_name='leavebalance',
            index=models.Index(fields=['employee', 'leave_type'], name='hrms_leave__employe_705178_idx'),
        ),
        migrations.AddIndex(
            model_name='leavebalance',
            index=models.Index(fields=['balance_year'], name='hrms_leave__balance_948721_idx'),
        ),
        migrations.AddIndex(
            model_name='leavebalance',
            index=models.Index(fields=['is_active'], name='hrms_leave__is_acti_0557d6_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='leavebalance',
            unique_together={('employee', 'leave_type', 'balance_year')},
        ),
        migrations.AddIndex(
            model_name='leavepolicy',
            index=models.Index(fields=['code'], name='hrms_leave__code_0d2bf4_idx'),
        ),
        migrations.AddIndex(
            model_name='leavepolicy',
            index=models.Index(fields=['is_active'], name='hrms_leave__is_acti_450da3_idx'),
        ),
        migrations.AddIndex(
            model_name='leavepolicy',
            index=models.Index(fields=['priority'], name='hrms_leave__priorit_bbeda1_idx'),
        ),
        migrations.AddIndex(
            model_name='leavepolicy',
            index=models.Index(fields=['effective_from', 'effective_to'], name='hrms_leave__effecti_1c67a9_idx'),
        ),
        migrations.AddIndex(
            model_name='leaverequest',
            index=models.Index(fields=['employee', 'start_date'], name='hrms_leave__employe_24e4c6_idx'),
        ),
        migrations.AddIndex(
            model_name='leaverequest',
            index=models.Index(fields=['status'], name='hrms_leave__status_612389_idx'),
        ),
        migrations.AddIndex(
            model_name='leaverequest',
            index=models.Index(fields=['leave_type'], name='hrms_leave__leave_t_b3141c_idx'),
        ),
        migrations.AddIndex(
            model_name='leaverequest',
            index=models.Index(fields=['start_date', 'end_date'], name='hrms_leave__start_d_a30332_idx'),
        ),
        migrations.AddIndex(
            model_name='leavetransaction',
            index=models.Index(fields=['leave_balance', 'processed_at'], name='hrms_leave__leave_b_e9b0e7_idx'),
        ),
        migrations.AddIndex(
            model_name='leavetransaction',
            index=models.Index(fields=['transaction_type'], name='hrms_leave__transac_08f1c7_idx'),
        ),
        migrations.AddIndex(
            model_name='leavetransaction',
            index=models.Index(fields=['reference_id'], name='hrms_leave__referen_5753ec_idx'),
        ),
        migrations.AddIndex(
            model_name='salarycomponent',
            index=models.Index(fields=['code'], name='hrms_salary_code_b6609b_idx'),
        ),
        migrations.AddIndex(
            model_name='salarycomponent',
            index=models.Index(fields=['component_type'], name='hrms_salary_compone_676b83_idx'),
        ),
        migrations.AddIndex(
            model_name='salarycomponent',
            index=models.Index(fields=['category'], name='hrms_salary_categor_7d0efb_idx'),
        ),
        migrations.AddIndex(
            model_name='salarycomponent',
            index=models.Index(fields=['is_active'], name='hrms_salary_is_acti_7ced61_idx'),
        ),
        migrations.AddIndex(
            model_name='workshift',
            index=models.Index(fields=['code'], name='hrms_work_s_code_63c6f2_idx'),
        ),
        migrations.AddIndex(
            model_name='workshift',
            index=models.Index(fields=['shift_type'], name='hrms_work_s_shift_t_7bbf13_idx'),
        ),
        migrations.AddIndex(
            model_name='workshift',
            index=models.Index(fields=['is_active'], name='hrms_work_s_is_acti_54be2a_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeshiftassignment',
            index=models.Index(fields=['employee', 'start_date'], name='hrms_employ_employe_8ed243_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeshiftassignment',
            index=models.Index(fields=['work_shift'], name='hrms_employ_work_sh_1b0788_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeshiftassignment',
            index=models.Index(fields=['status'], name='hrms_employ_status_d30931_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeshiftassignment',
            index=models.Index(fields=['start_date', 'end_date'], name='hrms_employ_start_d_3a6059_idx'),
        ),
    ]
